.dashboard-container {
  margin: 20px;
  height: 100%;
  animation: fadeIn 0.5s ease-out;
}

/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

/* Main Grid */
.main-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-6);
}

/* Responsive Design */
@media (max-width: 1023px) {
  .main-grid {
    grid-template-columns: 1fr;
  }
}
