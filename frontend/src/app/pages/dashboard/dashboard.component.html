<div class="dashboard-container">
  <app-page-header
    title="Dashboard"
    subtitle="Welcome back! Here's what's happening with your store today.">
    <ng-template>
      <app-button
        (onClick)="onRefreshData()"
        [disabled]="loading"
        variant="secondary"
        size="small"
        icon="🔄"
        text="Refresh"
      >
      </app-button>
      <app-button
        href="/sales/create"
        text="New Sale"
        icon="➕"
        variant="primary"
        size="small"
      >
      </app-button>
    </ng-template>
  </app-page-header>

  <app-loading-spinner *ngIf="loading" size="xl" message="Loading dashboard data..."></app-loading-spinner>


  <div *ngIf="!loading" class="dashboard-content">
    <app-stats></app-stats>

    <div class="main-grid">
      <app-quick-actions></app-quick-actions>
      <app-recent-activity></app-recent-activity>
    </div>

    <app-explore-component></app-explore-component>
  </div>
</div>
