import { Routes } from '@angular/router';
import { DashboardComponent } from './pages/dashboard/dashboard.component';

export const routes: Routes = [
  // Default redirect to dashboard
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'dashboard'
  },

  // Dashboard
  {
    path: 'dashboard',
    component: DashboardComponent
  },

  // Products Module
  {
    path: 'products',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/products/product-list/product-list.component').then(m => m.ProductListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/products/product-create/product-create.component').then(m => m.ProductCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/products/product-edit/product-edit.component').then(m => m.ProductEditComponent)
      },
      {
        path: ':id/view',
        loadComponent: () => import('./pages/products/product-detail/product-detail.component').then(m => m.ProductDetailComponent)
      }
    ]
  },

  // Categories Module
  {
    path: 'categories',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/categories/category-list/category-list.component').then(m => m.CategoryListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/categories/category-create/category-create.component').then(m => m.CategoryCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/categories/category-edit/category-edit.component').then(m => m.CategoryEditComponent)
      }
    ]
  },

  // Tags Module
  {
    path: 'tags',
    children: [
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full'
      },
      {
        path: 'list',
        loadComponent: () => import('./pages/tags/tag-list/tag-list.component').then(m => m.TagListComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/tags/tag-create/tag-create.component').then(m => m.TagCreateComponent)
      },
      {
        path: ':id/edit',
        loadComponent: () => import('./pages/tags/tag-edit/tag-edit.component').then(m => m.TagEditComponent)
      }
    ]
  },

  // Inventory Module
  {
    path: 'inventory',
    children: [
      {
        path: '',
        redirectTo: 'current',
        pathMatch: 'full'
      },
      {
        path: 'current',
        loadComponent: () => import('./pages/inventory/current-inventory/current-inventory.component').then(m => m.CurrentInventoryComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./pages/inventory/inventory-history/inventory-history.component').then(m => m.InventoryHistoryComponent)
      },
      {
        path: 'reports',
        loadComponent: () => import('./pages/inventory/inventory-reports/inventory-reports.component').then(m => m.InventoryReportsComponent)
      }
    ]
  },

  // Sales Module
  {
    path: 'sales',
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./pages/sales/sales-dashboard/sales-dashboard.component').then(m => m.SalesDashboardComponent)
      },
      {
        path: 'active',
        loadComponent: () => import('./pages/sales/active-sales/active-sales.component').then(m => m.ActiveSalesComponent)
      },
      {
        path: 'history',
        loadComponent: () => import('./pages/sales/sales-history/sales-history.component').then(m => m.SalesHistoryComponent)
      },
      {
        path: 'create',
        loadComponent: () => import('./pages/sales/create-sale/create-sale.component').then(m => m.CreateSaleComponent)
      }
    ]
  },

  // Analytics Module
  {
    path: 'analytics',
    children: [
      {
        path: '',
        redirectTo: 'dashboard',
        pathMatch: 'full'
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./pages/analytics/analytics-dashboard/analytics-dashboard.component').then(m => m.AnalyticsDashboardComponent)
      },
      {
        path: 'sales',
        loadComponent: () => import('./pages/analytics/sales-analytics/sales-analytics.component').then(m => m.SalesAnalyticsComponent)
      },
      {
        path: 'inventory',
        loadComponent: () => import('./pages/analytics/inventory-analytics/inventory-analytics.component').then(m => m.InventoryAnalyticsComponent)
      }
    ]
  },

  // Settings Module
  {
    path: 'settings',
    children: [
      {
        path: '',
        redirectTo: 'general',
        pathMatch: 'full'
      },
      {
        path: 'general',
        loadComponent: () => import('./pages/settings/general-settings/general-settings.component').then(m => m.GeneralSettingsComponent)
      },
      {
        path: 'user',
        loadComponent: () => import('./pages/settings/user-settings/user-settings.component').then(m => m.UserSettingsComponent)
      },
      {
        path: 'system',
        loadComponent: () => import('./pages/settings/system-settings/system-settings.component').then(m => m.SystemSettingsComponent)
      },
      {
        path: 'integrations',
        loadComponent: () => import('./pages/settings/integration-settings/integration-settings.component').then(m => m.IntegrationSettingsComponent)
      }
    ]
  },

  // Profile and Account
  {
    path: 'profile',
    loadComponent: () => import('./pages/profile/profile.component').then(m => m.ProfileComponent)
  },

  // Help and Support
  {
    path: 'help',
    loadComponent: () => import('./pages/help/help.component').then(m => m.HelpComponent)
  },

  // Activity Log
  {
    path: 'activity',
    loadComponent: () => import('./pages/activity/activity-log.component').then(m => m.ActivityLogComponent)
  },

  // Wildcard route - must be last
  {
    path: '**',
    loadComponent: () => import('./pages/not-found/not-found.component').then(m => m.NotFoundComponent)
  }
];
