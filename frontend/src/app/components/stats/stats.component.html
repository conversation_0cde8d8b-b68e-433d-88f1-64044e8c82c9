<div class="stats-grid">
  <app-stats-card
    [icon]="getStatIcon('totalProducts')"
    [value]="formatNumber(stats.totalProducts)"
    label="Total Products"
    trend="positive"
    trendValue="+12%"
    [color]="getStatColor('totalProducts')"
  ></app-stats-card>

  <app-stats-card
    [icon]="getStatIcon('totalCategories')"
    [value]="formatNumber(stats.totalCategories)"
    label="Categories"
    trend="positive"
    trendValue="+3"
    [color]="getStatColor('totalCategories')"
  ></app-stats-card>

  <app-stats-card
    [icon]="getStatIcon('totalSales')"
    [value]="formatNumber(stats.totalSales)"
    label="Sales Today"
    trend="positive"
    trendValue="+8%"
    [color]="getStatColor('totalSales')"
  ></app-stats-card>

  <app-stats-card
    [icon]="getStatIcon('totalRevenue')"
    [value]="formatCurrency(stats.totalRevenue)"
    label="Revenue Today"
    trend="positive"
    trendValue="+15%"
    [color]="getStatColor('totalRevenue')"
  ></app-stats-card>

  <app-stats-card
    [icon]="getStatIcon('lowStockItems')"
    [value]="formatNumber(stats.lowStockItems)"
    label="Low Stock Items"
    trend="warning"
    trendValue="Alert"
    [color]="getStatColor('lowStockItems')"
  ></app-stats-card>

  <app-stats-card
    [icon]="getStatIcon('pendingSales')"
    [value]="formatNumber(stats.pendingSales)"
    label="Pending Sales"
    trend="neutral"
    trendValue="Pending"
    [color]="getStatColor('pendingSales')"
  ></app-stats-card>
</div>
