import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatsCardComponent } from '../stats-card/stats-card.component';

interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalSales: number;
  totalRevenue: number;
  lowStockItems: number;
  pendingSales: number;
}

@Component({
  selector: 'app-stats',
  standalone: true,
  imports: [CommonModule, StatsCardComponent],
  templateUrl: './stats.component.html',
  styleUrl: './stats.component.css'
})
export class StatsComponent {
  stats: DashboardStats = {
    totalProducts: 1247,
    totalCategories: 23,
    totalSales: 89,
    totalRevenue: 45678.90,
    lowStockItems: 12,
    pendingSales: 5
  };

  getStatIcon(statType: string): string {
    const icons: { [key: string]: string } = {
      totalProducts: '📦',
      totalCategories: '📂',
      totalSales: '💰',
      totalRevenue: '💵',
      lowStockItems: '⚠️',
      pendingSales: '⏳'
    };
    return icons[statType] || '📊';
  }

  getStatColor(statType: string): string {
    const colors: { [key: string]: string } = {
      totalProducts: 'primary',
      totalCategories: 'secondary',
      totalSales: 'success',
      totalRevenue: 'success',
      lowStockItems: 'warning',
      pendingSales: 'info'
    };
    return colors[statType] || 'primary';
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatNumber(num: number): string {
    return new Intl.NumberFormat('en-US').format(num);
  }
}
