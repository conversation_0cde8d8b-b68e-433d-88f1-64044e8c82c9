import { Component, Input } from '@angular/core';
import {NgIf} from "@angular/common";
import {ActionComponent} from '../ui/action/action.component';

@Component({
  selector: 'app-placeholder',
  imports: [
    NgIf,
    ActionComponent
  ],
  templateUrl: './placeholder.component.html',
  styleUrl: './placeholder.component.css'
})
export class PlaceholderComponent {
  @Input() title = 'Coming Soon';
  @Input() subtitle = 'This feature is under development';
  @Input() icon = '🚧';
  @Input() description = 'We\'re working hard to bring you this feature. Check back soon for updates!';
  @Input() showBackButton = true;
  @Input() backRoute = '/dashboard';
  @Input() backLabel = 'Back to Dashboard';
}
