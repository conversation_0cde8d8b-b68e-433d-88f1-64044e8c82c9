/* Quick Actions Grid */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-4);
}

/* Quick Action Card */
.quick-action-card {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding: var(--space-4);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all var(--transition-slow);
  background-color: var(--bg-surface);
  position: relative;
  overflow: hidden;
}

/* Quick Action Card Pseudo-element */
.quick-action-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: var(--primary-500);
  transition: all var(--transition-slow);
}

/* Quick Action Card Hover Effects */
.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
}

.quick-action-card:hover::before {
  width: 100%;
  opacity: 0.2;
}

/* Action Icon */
.action-icon {
  font-size: var(--text-2xl);
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-lg);
  background-color: var(--bg-primary);
  flex-shrink: 0;
}

/* Action Content */
.action-content {
  flex: 1;
}

/* Action Title */
.action-title {
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

/* Action Description */
.action-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

/* Color variants */
.action-primary::before {
  background-color: var(--primary-500);
}

.action-success::before {
  background-color: var(--success-500);
}

.action-info::before {
  background-color: var(--info-500);
}

.action-warning::before {
  background-color: var(--warning-500);
}

.action-secondary::before {
  background-color: var(--secondary-500);
}


@media (max-width: 767px) {
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
