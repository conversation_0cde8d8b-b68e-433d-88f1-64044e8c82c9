<div class="card">
  <div class="card-header">
    <h3 class="card-title">Quick Actions</h3>
    <p class="card-subtitle">Frequently used actions for faster workflow</p>
  </div>
  <div class="card-body">
    <div class="quick-actions-grid">
      <a
        *ngFor="let action of quickActions; trackBy: trackByActionTitle"
        [routerLink]="action.route"
        class="quick-action-card"
        [class]="'action-' + action.color"
      >
        <div class="action-icon">{{ action.icon }}</div>
        <div class="action-content">
          <div class="action-title">{{ action.title }}</div>
          <div class="action-description">{{ action.description }}</div>
        </div>
      </a>
    </div>
  </div>
</div>
