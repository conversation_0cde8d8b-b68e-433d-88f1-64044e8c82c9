import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface QuickAction {
  title: string;
  description: string;
  icon: string;
  route: string;
  color: 'primary' | 'success' | 'info' | 'warning' | 'secondary';
}

@Component({
  selector: 'app-quick-actions',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './quick-actions.component.html',
  styleUrl: './quick-actions.component.css'
})
export class QuickActionsComponent {
  quickActions: QuickAction[] = [
    {
      title: 'Add Product',
      description: 'Create a new product',
      icon: '📦',
      route: '/products/create',
      color: 'primary'
    },
    {
      title: 'New Sale',
      description: 'Process a new sale',
      icon: '💰',
      route: '/sales/create',
      color: 'success'
    },
    {
      title: 'Check Inventory',
      description: 'View current stock levels',
      icon: '📋',
      route: '/inventory/current',
      color: 'info'
    },
    {
      title: 'Add Category',
      description: 'Create a new category',
      icon: '📂',
      route: '/categories/create',
      color: 'secondary'
    },
    {
      title: 'Sales Report',
      description: 'View sales analytics',
      icon: '📊',
      route: '/sales/reports',
      color: 'warning'
    },
    {
      title: 'Settings',
      description: 'Configure system settings',
      icon: '⚙️',
      route: '/settings',
      color: 'secondary'
    }
  ];

  trackByActionTitle(_index: number, action: QuickAction): string {
    return action.title;
  }
}
