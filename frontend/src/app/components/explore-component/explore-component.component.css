/* Navigation Cards */
.nav-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--space-6);
}

.nav-card {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding: var(--space-6);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  text-decoration: none;
  transition: all var(--transition-base);
  background: linear-gradient(135deg, var(--bg-surface) 0%, var(--bg-surface-hover) 100%);
  position: relative;
  overflow: hidden;
}

.nav-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-500), var(--secondary-500));
  transform: scaleX(0);
  transition: transform var(--transition-base);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  text-decoration: none;
  border-color: var(--primary-200);
}

.nav-card:hover::before {
  transform: scaleX(1);
}

.nav-card-icon {
  font-size: var(--text-3xl);
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-50), var(--primary-100));
  border-radius: var(--radius-xl);
  color: var(--primary-700);
  flex-shrink: 0;
}

.nav-card-content {
  flex: 1;
}

.nav-card-title {
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.nav-card-description {
  font-size: var(--text-sm);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  line-height: 1.4;
}

.nav-card-stats {
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  color: var(--primary-600);
  background-color: var(--primary-50);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  display: inline-block;
}

@media (max-width: 767px) {
  .nav-cards-grid {
    grid-template-columns: 1fr;
  }
}
