<div class="card">
  <div class="card-header">
    <h3 class="card-title">Explore The Store</h3>
    <p class="card-subtitle">Navigate to different sections of your store</p>
  </div>
  <div class="card-body">
    <div class="nav-cards-grid">
      <a
        *ngFor="let card of navigationCards; trackBy: trackByCardTitle"
        [routerLink]="card.route"
        class="nav-card"
      >
        <div class="nav-card-icon">{{ card.icon }}</div>
        <div class="nav-card-content">
          <div class="nav-card-title">{{ card.title }}</div>
          <div class="nav-card-description">{{ card.description }}</div>
          <div *ngIf="card.stats" class="nav-card-stats">{{ card.stats }}</div>
        </div>
      </a>
    </div>
  </div>
</div>
