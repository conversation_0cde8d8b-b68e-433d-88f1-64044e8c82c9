import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface NavigationCard {
  title: string;
  description: string;
  icon: string;
  route: string;
  stats?: string;
}

@Component({
  selector: 'app-explore-component',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './explore-component.component.html',
  styleUrl: './explore-component.component.css'
})
export class ExploreComponentComponent {
  navigationCards: NavigationCard[] = [
    {
      title: 'Products',
      description: 'Manage your product catalog and inventory',
      icon: '📦',
      route: '/products',
      stats: '11232 Products'
    },
    {
      title: 'Inventory',
      description: 'Track stock levels and manage inventory',
      icon: '📋',
      route: '/inventory',
      stats: ''
    },
    {
      title: 'Sales',
      description: 'Process sales and view transaction history',
      icon: '💰',
      route: '/sales',
      stats: ''
    },
    {
      title: 'Analytics',
      description: 'View detailed reports and insights',
      icon: '📊',
      route: '/analytics',
      stats: ''
    }
  ];

  trackByCardTitle(_index: number, card: NavigationCard): string {
    return card.title;
  }
}
