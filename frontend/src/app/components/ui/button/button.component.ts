import {Component, EventEmitter, Input, Output} from '@angular/core';
import {NgClass} from '@angular/common';
import {Router} from '@angular/router';

@Component({
  selector: 'app-button',
  imports: [
    NgClass,
  ],
  templateUrl: './button.component.html',
  styleUrl: './button.component.css'
})
export class ButtonComponent {
  @Input() text: string = '';
  @Input() icon: string = '';
  @Input() size: string | 'small' | 'medium' | 'large' = 'medium';
  @Input() variant: string | 'ghost' | 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info' = 'ghost';
  @Input() hover: string | 'none' | 'translate' | 'rotate' = 'translate';
  @Input() disabled: boolean = false;
  @Input() href?: string;
  @Output() onClick: EventEmitter<any> = new EventEmitter<any>();

  constructor(private router: Router) {
  }
  handleClick() {
    this.onClick.emit();
    if (this.href) {
      this.router.navigateByUrl(this.href);
    }
  }
}
