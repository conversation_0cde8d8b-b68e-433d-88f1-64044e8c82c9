<button
  class="btn"
  [disabled]="disabled"
  [ngClass]="{
    'btn-icon': !text,
    'btn-sm': size === 'small',
    'btn-lg': size === 'large',
    'btn-translate': hover === 'translate',
    'btn-rotate': hover === 'rotate',
    'btn-ghost': variant === 'ghost',
    'btn-primary': variant === 'primary',
    'btn-secondary': variant === 'secondary',
    'btn-success': variant === 'success',
    'btn-error': variant === 'error',
    'btn-warning': variant === 'warning',
    'btn-info': variant === 'info'
  }"
  (click)="handleClick()"
>
  <span>{{icon}}</span> {{text}}
</button>
