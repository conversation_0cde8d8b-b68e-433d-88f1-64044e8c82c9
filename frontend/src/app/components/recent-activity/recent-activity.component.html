<div class="card">
  <div class="card-header">
    <h3 class="card-title">Recent Activity</h3>
    <p class="card-subtitle">Latest updates and notifications</p>
  </div>
  <div class="card-body">
    <div class="activity-list">
      <div
        *ngFor="let activity of activities; trackBy: trackByActivityId"
        class="activity-item"
        [class]="'activity-' + activity.type"
      >
        <div class="activity-icon">{{ activity.icon }}</div>
        <div class="activity-content">
          <div class="activity-message">{{ activity.message }}</div>
          <div class="activity-time">{{ getRelativeTime(activity.timestamp) }}</div>
        </div>
      </div>

      <div *ngIf="activities.length === 0" class="empty-state">
        <p>No recent activity</p>
      </div>
    </div>
    <div class="activity-footer">
      <a routerLink="/activity" class="view-all-link">
        View all activity →
      </a>
    </div>
  </div>
</div>
