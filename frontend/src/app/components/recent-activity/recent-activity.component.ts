import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface RecentActivity {
  id: string;
  type: 'sale' | 'product' | 'inventory' | 'user';
  message: string;
  timestamp: Date;
  icon: string;
}

@Component({
  selector: 'app-recent-activity',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './recent-activity.component.html',
  styleUrl: './recent-activity.component.css'
})
export class RecentActivityComponent {
  activities: RecentActivity[] = [
    {
      id: '1',
      type: 'sale',
      message: 'New sale completed for $234.50',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      icon: '💰'
    },
    {
      id: '2',
      type: 'product',
      message: 'Product "Wireless Headphones" added to inventory',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      icon: '📦'
    },
    {
      id: '3',
      type: 'inventory',
      message: 'Low stock alert: "Gaming Mouse" (3 items left)',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      icon: '⚠️'
    },
    {
      id: '4',
      type: 'user',
      message: 'New user "John <PERSON>" registered',
      timestamp: new Date(Date.now() - 45 * 60 * 1000),
      icon: '👤'
    },
    {
      id: '5',
      type: 'sale',
      message: 'Sale #1234 marked as completed',
      timestamp: new Date(Date.now() - 60 * 60 * 1000),
      icon: '✅'
    }
  ];

  trackByActivityId(_index: number, activity: RecentActivity): string {
    return activity.id;
  }

  getRelativeTime(date: Date): string {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  }
}
