.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-4);
  padding-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.activity-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  background-color: var(--bg-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-xl);
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: var(--text-sm);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  line-height: 1.4;
}

.activity-time {
  font-size: var(--text-xs);
  color: var(--text-primary);
}

.activity-footer {
  margin-top: var(--space-6);
  text-align: center;
  padding-top: var(--space-4);
  border-top: 1px solid var(--border-primary);
}

.view-all-link {
  font-size: var(--text-sm);
  color: var(--primary-600);
  text-decoration: none;
  font-weight: var(--font-medium);
  transition: color var(--transition-base);
}

.view-all-link:hover {
  color: var(--primary-700);
  text-decoration: underline;
}

/* Activity type styles */
.activity-sale .activity-icon {
  background-color: var(--success-50);
  color: var(--color-success-600);
}

.activity-product .activity-icon {
  background-color: var(--primary-50);
  color: var(--primary-600);
}

.activity-inventory .activity-icon {
  background-color: var(--warning-50);
  color: var(--color-warning-600);
}

.activity-user .activity-icon {
  background-color: var(--info-50);
  color: var(--color-info-600);
}

.empty-state {
  text-align: center;
  padding: var(--space-6);
  color: var(--text-tertiary);
  font-size: var(--text-sm);
}
