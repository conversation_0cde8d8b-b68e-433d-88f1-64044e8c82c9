.page-header {
  margin-bottom: var(--space-8);
}

.page-header-content {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--space-6);
}

.page-title-section {
  flex: 1;
}

.page-title {
  margin: 0 0 var(--space-2) 0;
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-600), var(--secondary-600));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  margin: 0;
  font-size: var(--text-lg);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
}

.page-actions {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  flex-shrink: 0;
}


@media (max-width: 1023px) {
  .page-header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-4);
  }

  .page-actions {
    justify-content: flex-start;
  }
}

@media (max-width: 767px) {
  .page-title {
    font-size: var(--text-3xl);
  }
}
