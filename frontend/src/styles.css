@import "./styles/variables.css";

:root {
  /* Color Palette - Modern Material Design 3 Inspired */

  /* Primary Colors - Modern Blue */
  --primary-50: #e3f2fd;
  --primary-100: #bbdefb;
  --primary-200: #90caf9;
  --primary-300: #64b5f6;
  --primary-400: #42a5f5;
  --primary-500: #2196f3;
  --primary-600: #1e88e5;
  --primary-700: #1976d2;
  --primary-800: #1565c0;
  --primary-900: #0d47a1;

  /* Secondary Colors - Modern Teal */
  --secondary-50: #e0f2f1;
  --secondary-100: #b2dfdb;
  --secondary-200: #80cbc4;
  --secondary-300: #4db6ac;
  --secondary-400: #26a69a;
  --secondary-500: #009688;
  --secondary-600: #00897b;
  --secondary-700: #00796b;
  --secondary-800: #00695c;
  --secondary-900: #004d40;

  /* Neutral Colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  /* Semantic Colors */
  --success-50: #e8f5e8;
  --success-500: #4caf50;
  --success-700: #388e3c;

  --warning-50: #fff8e1;
  --warning-500: #ff9800;
  --warning-700: #f57c00;

  --error-50: #ffebee;
  --error-500: #f44336;
  --error-700: #d32f2f;

  --info-50: #e3f2fd;
  --info-500: #2196f3;
  --info-700: #1976d2;

  /* Typography Scale */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Font Sizes - Modern Scale */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;

  /* Spacing Scale - 8px base */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */

  /* Border Radius */
  --radius-sm: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;  /* 6px */
  --radius-lg: 0.5rem;    /* 8px */
  --radius-xl: 0.75rem;   /* 12px */
  --radius-2xl: 1rem;     /* 16px */
  --radius-3xl: 1.5rem;   /* 24px */
  --radius-full: 9999px;

  /* Shadows - Modern Elevation */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-base: 250ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* Container Sizes */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}

/* ===== LIGHT THEME ===== */
:root,
[data-theme="light"] {
  --bg-primary: var(--neutral-50);
  --bg-secondary: var(--neutral-100);
  --bg-surface: #ffffff;
  --bg-surface-hover: var(--neutral-50);
  --bg-surface-active: var(--neutral-100);

  --text-primary: var(--neutral-900);
  --text-secondary: var(--neutral-700);
  --text-muted: var(--neutral-500);
  --text-inverse: #ffffff;

  --border-primary: var(--neutral-200);
  --border-secondary: var(--neutral-300);
  --border-focus: var(--primary-500);

  --interactive-primary: var(--primary-600);
  --interactive-primary-hover: var(--primary-700);
  --interactive-secondary: var(--secondary-600);
  --interactive-secondary-hover: var(--secondary-700);
}

/* ===== DARK THEME ===== */
[data-theme="dark"] {
  --bg-primary: var(--neutral-900);
  --bg-secondary: var(--neutral-800);
  --bg-surface: var(--neutral-900);
  --bg-surface-hover: var(--neutral-700);
  --bg-surface-active: var(--neutral-600);

  --text-primary: var(--neutral-100);
  --text-secondary: var(--neutral-300);
  --text-muted: var(--neutral-400);
  --text-inverse: var(--neutral-900);

  --border-primary: var(--neutral-700);
  --border-secondary: var(--neutral-600);
  --border-focus: var(--primary-400);

  --interactive-primary: var(--primary-400);
  --interactive-primary-hover: var(--primary-300);
  --interactive-secondary: var(--secondary-400);
  --interactive-secondary-hover: var(--secondary-300);

  /* Dark theme shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: var(--radius-full);
  transition: background-color var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--neutral-600);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-500);
}



body {
  margin: 0;
  padding: 0;
  min-height: 100dvh;
  height: fit-content;
  font-family: var(--font-family-primary);
  background-color: var(--bg-primary);
}


.hidden {
  display: none !important;
}

.card {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-base);
  overflow: hidden;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-primary);
  background-color: var(--bg-surface);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-secondary);
}

.card-title {
  margin: 0;
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.card-subtitle {
  margin: var(--space-1) 0 0 0;
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.card-body {
  padding: var(--space-6);
}
